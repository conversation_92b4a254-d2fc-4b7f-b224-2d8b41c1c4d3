# 许可证生成工具使用说明

## 概述

`generate_license.sh` 是一个用于生成和签名许可证XML文件的Shell脚本工具。该工具可以更新许可证的硬件序列号、开始日期和结束日期，并对XML文件进行签名和验证。只能在Linux系统上运行。

## 使用方法

```bash
./generate_license.sh <xml_file> [serial_number] [duration] [start_date]
```

### 参数说明

1. `xml_file` (必需): 要处理的XML许可证文件路径
2. `serial_number`: 硬件序列号, 如果输入""，则xml文件中没有硬件序列号节点
3. `duration` (可选): 许可证有效期时长，格式为数字+单位：
   - `s` - 秒
   - `m` - 分钟
   - `h` - 小时
   - `d` - 天
   - `M` - 月
   - `y` - 年
4. `start_date` (可选): 许可证开始日期，格式为 YYYY-MM-DD。如果未指定，则使用当前时间

### 使用示例

```bash
# 基本用法：仅指定XML文件,无有效性限制
./generate_license.sh sample.xml

# 指定XML文件和硬件序列号，无有效性限制
./generate_license.sh sample.xml TPJQF11A0024

# 指定XML文件、硬件序列号和有效期（2天）
./generate_license.sh sample.xml TPJQF11A0024 2d

# 指定XML文件和有效期（不指定硬件序列号时）
./generate_license.sh sample.xml "" 2h

# 指定XML文件、硬件序列号、有效期和开始日期
./generate_license.sh sample.xml TPJQF11A0024 2y 2025-01-01

# 指定XML文件、有效期和开始日期（不指定硬件序列号时）
./generate_license.sh sample.xml "" 2y 2025-01-01
```

## 功能说明

### 硬件序列号处理

- 如果提供了硬件序列号参数，脚本会生成对应的SHA-256哈希值
- 如果XML文件中已存在`HardwareSerialNumber`节点，则更新其内容
- 如果XML文件中不存在`HardwareSerialNumber`节点，则在适当位置添加该节点
- 如果未提供硬件序列号参数，则使用默认值"DEFAULT_SERIAL"

### 日期处理

当指定有效期参数时，脚本会自动处理许可证的开始日期和结束日期：

- **结束日期(EndDate)**: 如果指定了开始日期，则设置为(开始日期 + 有效期)；否则设置为(当前时间 + 有效期)
- **开始日期(StartDate)**: 如果指定了开始日期，则使用该日期；否则使用当前时间

如果XML文件中已存在这些日期节点，则更新为新值；如果不存在，则在适当位置添加这些节点，且保证StartDate节点在EndDate节点之前。

### 文件处理流程

1. 生成硬件序列号的SHA-256哈希值
2. 复制原始XML文件并添加"_signed"后缀
3. 更新或添加`HardwareSerialNumber`节点
4. 如果指定了有效期，则更新或添加`StartDate`和`EndDate`节点
5. 对生成的XML文件进行签名
6. 验证签名的XML文件

## 输出文件

脚本会生成一个带签名的XML文件，文件名格式为：`原文件名_signed.xml`

例如，如果输入文件是`mylicense.xml`，则输出文件为`mylicense_signed.xml`。

## 注意事项

1. 确保脚本具有执行权限：`chmod +x generate_license.sh`
2. 确保有权限读取输入文件和写入输出文件
3. 日期格式使用UTC时区格式：`YYYY-MM-DDTHH:MM:SSZ`
4. 开始日期格式支持带或不带前导零的月份和日期（如：2025-1-1 或 2025-01-01）
