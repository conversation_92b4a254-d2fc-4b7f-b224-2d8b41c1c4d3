#!/bin/bash

# Check if at least XML file is provided
if [ $# -lt 1 ] || [ $# -gt 4 ]; then
    echo "Usage: $0 <xml_file> [serial_number] [duration] [start_date]"
    echo "Example: $0 sample.xml TPJQF11A0024"
    echo "         $0 sample.xml"
    echo "         $0 sample.xml TPJQF11A0024 2d"
    echo "         $0 sample.xml TPJQF11A0024 2y 2025-01-01"
    echo "Duration format: 2s(second), 2m(minute), 2h(hour), 2d(day), 2M(month), 2y(year)"
    echo "Start date format: YYYY-MM-DD (optional)"
    exit 1
fi

# Ensure required tools have execute permissions
echo "Checking and setting execute permissions for tools..."
chmod +x ./sha256_tool ./license_tool ./verify_tool 2>/dev/null || echo "Warning: Could not set execute permissions for some tools"

XML_FILE=$1

# If serial number is provided, use it, otherwise generate from system
if [ $# -ge 2 ] && [ -n "$2" ]; then
    SERIAL_NUMBER=$2
    echo "Using provided serial number: ${SERIAL_NUMBER}"
else
    # Generate a default serial number or use system information
    SERIAL_NUMBER=""
    echo "Using default serial number: ${SERIAL_NUMBER}"
fi

# If duration is provided, use it
DURATION=""
if [ $# -ge 3 ] && [ -n "$3" ]; then
    DURATION=$3
    echo "Using provided duration: ${DURATION}"
fi

# If start date is provided, use it
START_DATE=""
if [ $# -eq 4 ] && [ -n "$4" ]; then
    START_DATE=$4
    echo "Using provided start date: ${START_DATE}"
    
    # Validate start date format
    if ! date -u -d "${START_DATE}" >/dev/null 2>&1; then
        echo "Error: Invalid start date format. Use YYYY-MM-DD format"
        exit 1
    fi
fi

# Check if XML file exists
if [ ! -f "${XML_FILE}" ]; then
    echo "Error: XML file '${XML_FILE}' not found"
    exit 1
fi

# Derive output file name by replacing extension with _signed.xml or appending _signed
OUTPUT_FILE="${XML_FILE%.*}_signed.xml"
if [ "${OUTPUT_FILE}" = "${XML_FILE}_signed.xml" ]; then
    OUTPUT_FILE="${XML_FILE}_signed"
fi

echo "Signing XML file: ${XML_FILE}"

# Step 1: Generate SHA-256 hash of the serial number
echo "Step 1: Generating SHA-256 hash for ${SERIAL_NUMBER}"
SHA256_OUTPUT=$(./sha256_tool "${SERIAL_NUMBER}")
SHA256_HASH=$(echo "$SHA256_OUTPUT" | awk '/SHA-256:/ {print $2}')

if [ -z "$SHA256_HASH" ]; then
    echo "Error: Failed to generate SHA-256 hash"
    exit 1
fi

echo "$SHA256_OUTPUT"

# Step 2: Copy input XML file to output file
echo "Step 2: Copying ${XML_FILE} to ${OUTPUT_FILE}"
cp "${XML_FILE}" "${OUTPUT_FILE}"

DynamicDistribution=false

# Step 3: Replace or add HardwareSerialNumber with the generated SHA-256 hash
echo "Step 3: Updating HardwareSerialNumber in ${OUTPUT_FILE}"
# Only process HardwareSerialNumber if a serial number was provided
if [ -n "$SERIAL_NUMBER" ] && [ "$SERIAL_NUMBER" != "" ]; then
    # Check if HardwareSerialNumber exists in the XML file
    if grep -q "<HardwareSerialNumber>" "${OUTPUT_FILE}"; then
        # Update existing HardwareSerialNumber in XML file
        sed -i "s#<HardwareSerialNumber>.*</HardwareSerialNumber>#<HardwareSerialNumber>${SHA256_HASH}</HardwareSerialNumber>#g" "${OUTPUT_FILE}"
    else
        # Add HardwareSerialNumber to the XML file (add before </AuthorizationTarget>)
        sed -i "/<\/AuthorizationTarget>/i\            <HardwareSerialNumber>${SHA256_HASH}</HardwareSerialNumber>" "${OUTPUT_FILE}"
    fi
    

else
    # If no serial number was provided, remove HardwareSerialNumber node if it exists
    sed -i "/<HardwareSerialNumber>.*<\/HardwareSerialNumber>/d" "${OUTPUT_FILE}"
    DynamicDistribution=true
fi

if grep -q "<DynamicDistribution>" "${OUTPUT_FILE}"; then
    # Update existing DynamicDistribution in XML file
    sed -i "s#<DynamicDistribution>.*</DynamicDistribution>#<DynamicDistribution>${DynamicDistribution}</DynamicDistribution>#g" "${OUTPUT_FILE}"
else
    # Add DynamicDistribution to the XML file (add before </AuthorizationTarget>)
    sed -i "/<\/AuthorizationTarget>/i\            <DynamicDistribution>${DynamicDistribution}</DynamicDistribution>" "${OUTPUT_FILE}"
fi

# Step 4: If duration is provided, update the EndDate
if [ -n "$DURATION" ]; then
    echo "Step 4: Updating EndDate based on duration: ${DURATION}"
    
    # Validate duration format
    if [[ ! "$DURATION" =~ ^[0-9]+[smhdMy]$ ]]; then
        echo "Error: Invalid duration format. Use format like 2s, 2m, 2h, 2d, 2M, 2y"
        exit 1
    fi
    
    # Extract number and unit
    NUMBER=$(echo "$DURATION" | sed 's/[smhdMy]$//')
    UNIT=$(echo "$DURATION" | sed 's/^[0-9]*//')
   
    
    # Calculate future date based on unit
    case "$UNIT" in
        s) 
            if [ -n "$START_DATE" ]; then
                PAST_DATE=$(date -u -d "${START_DATE}" +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "${START_DATE} +${NUMBER} seconds" +"%Y-%m-%dT%H:%M:%SZ")
            else
                PAST_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "+${NUMBER} seconds" +"%Y-%m-%dT%H:%M:%SZ")
            fi
            ;;
        m) 
            if [ -n "$START_DATE" ]; then
                PAST_DATE=$(date -u -d "${START_DATE}" +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "${START_DATE} +${NUMBER} minutes" +"%Y-%m-%dT%H:%M:%SZ")
            else
                PAST_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "+${NUMBER} minutes" +"%Y-%m-%dT%H:%M:%SZ")
            fi
            ;;
        h) 
            if [ -n "$START_DATE" ]; then
                PAST_DATE=$(date -u -d "${START_DATE}" +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "${START_DATE} +${NUMBER} hours" +"%Y-%m-%dT%H:%M:%SZ")
            else
                PAST_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "+${NUMBER} hours" +"%Y-%m-%dT%H:%M:%SZ")
            fi
            ;;
        d) 
            if [ -n "$START_DATE" ]; then
                PAST_DATE=$(date -u -d "${START_DATE}" +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "${START_DATE} +${NUMBER} days" +"%Y-%m-%dT%H:%M:%SZ")
            else
                PAST_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "+${NUMBER} days" +"%Y-%m-%dT%H:%M:%SZ")
            fi
            ;;
        M) 
            if [ -n "$START_DATE" ]; then
                PAST_DATE=$(date -u -d "${START_DATE}" +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "${START_DATE} +${NUMBER} months" +"%Y-%m-%dT%H:%M:%SZ")
            else
                PAST_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "+${NUMBER} months" +"%Y-%m-%dT%H:%M:%SZ")
            fi
            ;;
        y) 
            if [ -n "$START_DATE" ]; then
                PAST_DATE=$(date -u -d "${START_DATE}" +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "${START_DATE} +${NUMBER} years" +"%Y-%m-%dT%H:%M:%SZ")
            else
                PAST_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
                FUTURE_DATE=$(date -u -d "+${NUMBER} years" +"%Y-%m-%dT%H:%M:%SZ")
            fi
            ;;
        *) 
            echo "Error: Invalid duration unit. Use s, m, h, d, M, or y"
            exit 1
            ;;
    esac
    
    echo "Setting EndDate to: ${FUTURE_DATE}"
    echo "Setting StartDate to: ${PAST_DATE}"
    
    # Handle both StartDate and EndDate together to ensure proper order
    # Check if StartDate exists in the XML file
    if grep -q "<StartDate>" "${OUTPUT_FILE}"; then
        # Update existing StartDate in XML file
        if [ "$PAST_DATE" != "" ]; then
            sed -i "s#<StartDate>.*</StartDate>#<StartDate>${PAST_DATE}</StartDate>#g" "${OUTPUT_FILE}"
        else
            sed -i "/<StartDate>.*<\/StartDate>/d" "${OUTPUT_FILE}"
        fi
    else
        # Add StartDate to the XML file (add after LicenseDesc or at the beginning of LicenseHeader)
        if grep -q "<LicenseDesc>" "${OUTPUT_FILE}"; then
            # Add after LicenseDesc
            sed -i "/<LicenseDesc>.*<\/LicenseDesc>/a\    <StartDate>${PAST_DATE}</StartDate>" "${OUTPUT_FILE}"
        else
            # Add at the beginning of LicenseHeader section
            sed -i "/<LicenseHeader>/a\    <StartDate>${PAST_DATE}</StartDate>" "${OUTPUT_FILE}"
        fi
    fi

    # Check if EndDate exists in the XML file
    if grep -q "<EndDate>" "${OUTPUT_FILE}"; then
        # Update existing EndDate in XML file
        if [ "$FUTURE_DATE" != "" ]; then
            sed -i "s#<EndDate>.*</EndDate>#<EndDate>${FUTURE_DATE}</EndDate>#g" "${OUTPUT_FILE}"
        else
            sed -i "/<EndDate>.*<\/EndDate>/d" "${OUTPUT_FILE}"
        fi
    else
        # Add EndDate to the XML file (try to add after StartDate if it exists, otherwise add at the end of LicenseHeader)
        if grep -q "<StartDate>" "${OUTPUT_FILE}"; then
            # Add after StartDate
            sed -i "/<StartDate>.*<\/StartDate>/a\    <EndDate>${FUTURE_DATE}</EndDate>" "${OUTPUT_FILE}"
        else
            # Add at the end of LicenseHeader section
            sed -i "/<\/LicenseHeader>/i\    <EndDate>${FUTURE_DATE}</EndDate>" "${OUTPUT_FILE}"
        fi
    fi
fi

# Step 5: Sign the XML file
echo "Step 5: Signing the XML file"
./license_tool "${OUTPUT_FILE}"

if [ $? -ne 0 ]; then
    echo "Error: Failed to sign the XML file"
    exit 1
fi

# Step 6: Verify the signed XML file
echo "Step 6: Verifying the signed XML file"
./verify_tool "${OUTPUT_FILE}" "${SERIAL_NUMBER}"

if [ $? -ne 0 ]; then
    echo "Error: Failed to verify the signed XML file"
    exit 1
fi

echo "License generation completed successfully!"
echo "Output file: ${OUTPUT_FILE}"